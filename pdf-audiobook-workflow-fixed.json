{
  "name": "PDF Audiobook Generator & YouTube Uploader",
  "nodes": [
    {
      "parameters": {
        "triggerOn": "specificFolder",
        "event": "fileCreated",
        "folderToWatch": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_FOLDER_ID"
        }
      },
      "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "name": "Google Drive Trigger",
      "type": "n8n-nodes-base.googleDriveTrigger",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "jsCode": "const items = $input.all();\nconst validItems = [];\n\nfor (const item of items) {\n  const fileName = item.json.name || '';\n  const fileSize = item.json.size || 0;\n  const mimeType = item.json.mimeType || '';\n  \n  if (mimeType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf')) {\n    if (fileSize <= 50 * 1024 * 1024) {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: true,\n          validationMessage: 'File is valid PDF'\n        }\n      });\n    } else {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: false,\n          validationMessage: 'File too large (max 50MB)'\n        }\n      });\n    }\n  } else {\n    validItems.push({\n      json: {\n        ...item.json,\n        isValid: false,\n        validationMessage: 'File is not a PDF'\n      }\n    });\n  }\n}\n\nreturn validItems;"
      },
      "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
      "name": "File Validator",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
              "leftValue": "={{ $json.isValid }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
      "name": "If Valid PDF",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "download",
        "fileId": "={{ $json.id }}"
      },
      "id": "d4e5f6g7-h8i9-0123-defg-456789012345",
      "name": "Download PDF",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [900, 200]
    },
    {
      "parameters": {
        "binaryPropertyName": "data",
        "encrypted": false
      },
      "id": "e5f6g7h8-i9j0-1234-efgh-567890123456",
      "name": "Extract PDF Text",
      "type": "n8n-nodes-base.readPDF",
      "typeVersion": 1,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "jsCode": "const items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  let text = item.json.text || '';\n  \n  text = text.replace(/^\\s*\\d+\\s*$/gm, '');\n  text = text.replace(/^\\s*Page \\d+.*$/gm, '');\n  text = text.replace(/([a-z])\\n([A-Z])/g, '$1. $2');\n  text = text.replace(/\\n{3,}/g, '\\n\\n');\n  text = text.replace(/\\s+/g, ' ');\n  text = text.replace(/[""]/g, '\"');\n  text = text.replace(/['']/g, \"'\");\n  text = text.replace(/–/g, '-');\n  text = text.replace(/…/g, '...');\n  \n  const chunks = [];\n  const maxChunkSize = 4000;\n  \n  while (text.length > 0) {\n    if (text.length <= maxChunkSize) {\n      chunks.push(text.trim());\n      break;\n    }\n    \n    let cutPoint = text.lastIndexOf('.', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('!', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('?', maxChunkSize);\n    if (cutPoint === -1) cutPoint = maxChunkSize;\n    \n    chunks.push(text.substring(0, cutPoint + 1).trim());\n    text = text.substring(cutPoint + 1).trim();\n  }\n  \n  const fileName = item.json.fileName || item.json.name || 'Unknown';\n  const title = fileName.replace('.pdf', '').replace(/[_-]/g, ' ');\n  \n  processedItems.push({\n    json: {\n      ...item.json,\n      cleanedText: chunks.join(' '),\n      textChunks: chunks,\n      chunkCount: chunks.length,\n      title: title,\n      totalCharacters: chunks.join('').length\n    }\n  });\n}\n\nreturn processedItems;"
      },
      "id": "f6g7h8i9-j0k1-2345-fghi-678901234567",
      "name": "Text Preprocessor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 200]
    },
    {
      "parameters": {
        "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "xi-api-key",
              "value": "YOUR_ELEVENLABS_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "text",
              "value": "={{ $json.textChunks[0] }}"
            },
            {
              "name": "model_id",
              "value": "eleven_multilingual_v2"
            },
            {
              "name": "voice_settings",
              "value": {
                "stability": 0.5,
                "similarity_boost": 0.8,
                "style": 0.2,
                "use_speaker_boost": true
              }
            }
          ]
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "binaryData"
            }
          }
        }
      },
      "id": "g7h8i9j0-k1l2-3456-ghij-789012345678",
      "name": "Generate Audio (ElevenLabs)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [1560, 200]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "upload",
        "name": "={{ $json.title }}_audio.mp3",
        "parents": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_AUDIO_FOLDER_ID"
        },
        "binaryData": true,
        "binaryPropertyName": "data"
      },
      "id": "h8i9j0k1-l2m3-4567-hijk-890123456789",
      "name": "Upload Audio to Drive",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [1780, 200]
    },
    {
      "parameters": {
        "resource": "video",
        "operation": "upload",
        "title": "={{ $json.title }} - AI Generated Audiobook",
        "binaryProperty": "data",
        "categoryId": "27",
        "description": "This is an AI-generated audiobook version of {{ $json.title }}. Created using automated PDF to speech conversion.\\n\\n#audiobook #AI #textToSpeech",
        "tags": "audiobook,AI,text-to-speech,automated",
        "privacyStatus": "public"
      },
      "id": "************************************",
      "name": "Upload to YouTube",
      "type": "n8n-nodes-base.youTube",
      "typeVersion": 1,
      "position": [2000, 200]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "🎉 *Audiobook Generation Complete!*\\n\\n📚 **Title:** {{ $json.title }}\\n🎬 **YouTube URL:** https://youtube.com/watch?v={{ $json.id }}\\n\\n✅ Successfully uploaded to YouTube!",
        "additionalFields": {
          "parse_mode": "Markdown"
        }
      },
      "id": "j0k1l2m3-n4o5-6789-jklm-012345678901",
      "name": "Success Notification",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [2220, 200]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "❌ *PDF Processing Failed!*\\n\\n📚 **File:** {{ $json.name }}\\n🚫 **Error:** {{ $json.validationMessage }}\\n\\nPlease check the file and try again.",
        "additionalFields": {
          "parse_mode": "Markdown"
        }
      },
      "id": "k1l2m3n4-o5p6-7890-klmn-123456789012",
      "name": "Error Notification",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [900, 400]
    }
  ],
  "connections": {
    "Google Drive Trigger": {
      "main": [
        [
          {
            "node": "File Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "File Validator": {
      "main": [
        [
          {
            "node": "If Valid PDF",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Valid PDF": {
      "main": [
        [
          {
            "node": "Download PDF",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Error Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Download PDF": {
      "main": [
        [
          {
            "node": "Extract PDF Text",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract PDF Text": {
      "main": [
        [
          {
            "node": "Text Preprocessor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Text Preprocessor": {
      "main": [
        [
          {
            "node": "Generate Audio (ElevenLabs)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Audio (ElevenLabs)": {
      "main": [
        [
          {
            "node": "Upload Audio to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload Audio to Drive": {
      "main": [
        [
          {
            "node": "Upload to YouTube",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload to YouTube": {
      "main": [
        [
          {
            "node": "Success Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": {},
  "tags": ["audiobook", "automation", "pdf", "youtube"],
  "triggerCount": 1,
  "updatedAt": "2025-01-22T10:00:00.000Z",
  "versionId": "1"
}
