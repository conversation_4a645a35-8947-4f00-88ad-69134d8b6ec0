{
  "name": "PDF Audiobook Generator & YouTube Uploader",
  "nodes": [
    {
      "parameters": {
        "triggerOn": "specificFolder",
        "event": "fileCreated",
        "folderToWatch": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_FOLDER_ID"
        },
        "authentication": "oAuth2"
      },
      "id": "google-drive-trigger",
      "name": "Google Drive Trigger",
      "type": "n8n-nodes-base.googleDriveTrigger",
      "typeVersion": 1,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// File validation for PDF uploads\nconst items = $input.all();\nconst validItems = [];\n\nfor (const item of items) {\n  const fileName = item.json.name || '';\n  const fileSize = item.json.size || 0;\n  const mimeType = item.json.mimeType || '';\n  \n  // Check if file is PDF\n  if (mimeType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf')) {\n    // Check file size (max 50MB)\n    if (fileSize <= 50 * 1024 * 1024) {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: true,\n          validationMessage: 'File is valid PDF'\n        }\n      });\n    } else {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: false,\n          validationMessage: 'File too large (max 50MB)'\n        }\n      });\n    }\n  } else {\n    validItems.push({\n      json: {\n        ...item.json,\n        isValid: false,\n        validationMessage: 'File is not a PDF'\n      }\n    });\n  }\n}\n\nreturn validItems;"
      },
      "id": "file-validator",
      "name": "File Validator",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {\n          "options": {\n            "caseSensitive": true,\n            "leftValue": "",\n            "typeValidation": "strict"\n          },\n          "conditions": [\n            {\n              "id": "valid-pdf-condition",\n              "leftValue": "={{ $json.isValid }}",\n              "rightValue": true,\n              "operator": {\n                "type": "boolean",\n                "operation": "equals"\n              }\n            }\n          ],\n          "combinator": "and"\n        },\n        "options": {}\n      },\n      "id": "if-valid-pdf",\n      "name": "If Valid PDF",\n      "type": "n8n-nodes-base.if",\n      "typeVersion": 2,\n      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {\n        "resource": "file",\n        "operation": "download",\n        "fileId": "={{ $json.id }}",\n        "options": {}\n      },\n      "id": "download-pdf",\n      "name": "Download PDF",\n      "type": "n8n-nodes-base.googleDrive",\n      "typeVersion": 3,\n      "position": [
        900,
        200
      ]\n    },
    {
      "parameters": {
        "binaryPropertyName": "data",
        "encrypted": false
      },
      "id": "extract-pdf-text",
      "name": "Extract PDF Text",
      "type": "n8n-nodes-base.readPDF",
      "typeVersion": 1,
      "position": [
        1120,
        200
      ]
    },
    {
      "parameters": {
        "jsCode": "// Clean and preprocess extracted text\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  let text = item.json.text || '';\n  \n  // Remove headers, footers, page numbers\n  text = text.replace(/^\\s*\\d+\\s*$/gm, ''); // Remove standalone page numbers\n  text = text.replace(/^\\s*Page \\d+.*$/gm, ''); // Remove \"Page X\" lines\n  text = text.replace(/^\\s*Chapter \\d+.*$/gm, ''); // Keep chapter titles but clean them\n  \n  // Fix line breaks and paragraphs\n  text = text.replace(/([a-z])\\n([A-Z])/g, '$1. $2'); // Fix sentence breaks\n  text = text.replace(/\\n{3,}/g, '\\n\\n'); // Normalize paragraph breaks\n  text = text.replace(/\\s+/g, ' '); // Normalize whitespace\n  \n  // Handle special characters\n  text = text.replace(/[""]/g, '\"'); // Normalize quotes\n  text = text.replace(/['']/g, \"'\"); // Normalize apostrophes\n  text = text.replace(/–/g, '-'); // Normalize dashes\n  text = text.replace(/…/g, '...'); // Normalize ellipsis\n  \n  // Split into chunks for TTS (max 4000 characters per chunk)\n  const chunks = [];\n  const maxChunkSize = 4000;\n  \n  while (text.length > 0) {\n    if (text.length <= maxChunkSize) {\n      chunks.push(text.trim());\n      break;\n    }\n    \n    // Find the last sentence end within the chunk size\n    let cutPoint = text.lastIndexOf('.', maxChunkSize);\n    if (cutPoint === -1) {\n      cutPoint = text.lastIndexOf('!', maxChunkSize);\n    }\n    if (cutPoint === -1) {\n      cutPoint = text.lastIndexOf('?', maxChunkSize);\n    }\n    if (cutPoint === -1) {\n      cutPoint = maxChunkSize;\n    }\n    \n    chunks.push(text.substring(0, cutPoint + 1).trim());\n    text = text.substring(cutPoint + 1).trim();\n  }\n  \n  // Extract metadata\n  const fileName = item.json.fileName || 'Unknown';\n  const title = fileName.replace('.pdf', '').replace(/[_-]/g, ' ');\n  \n  processedItems.push({\n    json: {\n      ...item.json,\n      cleanedText: text,\n      textChunks: chunks,\n      chunkCount: chunks.length,\n      title: title,\n      totalCharacters: chunks.join('').length\n    }\n  });\n}\n\nreturn processedItems;"
      },
      "id": "text-preprocessor",
      "name": "Text Preprocessor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        200
      ]
    },
    {
      "parameters": {
        "url": "https://openrouter.ai/api/v1/chat/completions",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer YOUR_OPENROUTER_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "mistralai/mistral-7b-instruct"
            },
            {
              "name": "messages",
              "value": "={{ [{\"role\": \"system\", \"content\": \"You are a professional audiobook narrator. Convert the following text into natural, engaging speech suitable for text-to-speech. Add appropriate pauses with commas and periods. Make it flow naturally for audio.\"}, {\"role\": \"user\", \"content\": $json.textChunks[0]}] }}"
            },
            {
              "name": "max_tokens",
              "value": 4000
            },
            {
              "name": "temperature",
              "value": 0.7
            }
          ]
        },
        "options": {}
      },
      "id": "optimize-text-for-tts",
      "name": "Optimize Text for TTS",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1560,
        200
      ]
    },
    {
      "parameters": {
        "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "xi-api-key",
              "value": "YOUR_ELEVENLABS_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "text",
              "value": "={{ $json.choices[0].message.content }}"
            },
            {
              "name": "model_id",
              "value": "eleven_multilingual_v2"
            },
            {
              "name": "voice_settings",
              "value": {
                "stability": 0.5,
                "similarity_boost": 0.8,
                "style": 0.2,
                "use_speaker_boost": true
              }
            }
          ]
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "binaryData"
            }
          }
        }
      },
      "id": "generate-audio-elevenlabs",
      "name": "Generate Audio (ElevenLabs)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1780,
        200
      ]
    },
    {
      "parameters": {
        "url": "https://api.openai.com/v1/audio/speech",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer YOUR_OPENAI_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "tts-1"
            },
            {
              "name": "input",
              "value": "={{ $json.choices[0].message.content }}"
            },
            {
              "name": "voice",
              "value": "alloy"
            },
            {
              "name": "response_format",
              "value": "mp3"
            },
            {
              "name": "speed",
              "value": 1.0
            }
          ]
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "binaryData"
            }
          }
        }
      },
      "id": "generate-audio-openai",
      "name": "Generate Audio (OpenAI TTS)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1780,
        400
      ]
    },
    {
      "parameters": {
        "jsCode": "// Process multiple audio chunks and combine them\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  // Store audio data with chunk information\n  processedItems.push({\n    json: {\n      ...item.json,\n      audioGenerated: true,\n      audioFormat: 'mp3',\n      chunkIndex: 0, // This will be updated in loop processing\n      timestamp: new Date().toISOString()\n    },\n    binary: item.binary\n  });\n}\n\nreturn processedItems;"
      },
      "id": "audio-processor",
      "name": "Audio Processor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        2000,
        300
      ]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "upload",
        "name": "={{ $json.title }}_audio.mp3",
        "parents": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_AUDIO_FOLDER_ID"
        },
        "binaryData": true,
        "binaryPropertyName": "data"
      },
      "id": "upload-audio-to-drive",
      "name": "Upload Audio to Drive",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        2220,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Create video using FFmpeg commands\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  const title = item.json.title || 'Audiobook';\n  const audioUrl = item.json.webViewLink || '';\n  \n  // Create video generation parameters\n  const videoParams = {\n    title: title,\n    audioUrl: audioUrl,\n    duration: 300, // 5 minutes default\n    width: 1920,\n    height: 1080,\n    backgroundColor: '#1a1a1a',\n    textColor: '#ffffff',\n    fontSize: 48,\n    fontFamily: 'Arial'\n  };\n  \n  processedItems.push({\n    json: {\n      ...item.json,\n      videoParams: videoParams,\n      videoReady: false\n    }\n  });\n}\n\nreturn processedItems;"
      },
      "id": "prepare-video-generation",
      "name": "Prepare Video Generation",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        2440,
        300
      ]
    },
    {
      "parameters": {
        "url": "https://api.creatomate.com/v1/renders",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer YOUR_CREATOMATE_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "template_id",
              "value": "YOUR_CREATOMATE_TEMPLATE_ID"
            },
            {
              "name": "modifications",
              "value": {\n                \"Audio-1\": \"={{ $json.audioUrl }}\",\n                \"Text-1\": \"={{ $json.title }}\",\n                \"Background-1\": \"#1a1a1a\"\n              }\n            }\n          ]\n        },\n        \"options\": {}\n      },\n      \"id\": \"generate-video-creatomate\",\n      \"name\": \"Generate Video (Creatomate)\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 4.2,\n      \"position\": [\n        2660,\n        300\n      ]\n    },\n    {\n      \"parameters\": {\n        \"jsCode\": \"// Alternative FFmpeg video generation\\nconst items = $input.all();\\nconst processedItems = [];\\n\\nfor (const item of items) {\\n  const title = item.json.title || 'Audiobook';\\n  \\n  // FFmpeg command to create video with audio\\n  const ffmpegCommand = `ffmpeg -f lavfi -i color=c=black:s=1920x1080:d=300 -i \\\"${item.json.audioUrl}\\\" -c:v libx264 -c:a aac -shortest -vf \\\"drawtext=text='${title}':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=(h-text_h)/2\\\" output.mp4`;\\n  \\n  processedItems.push({\\n    json: {\\n      ...item.json,\\n      ffmpegCommand: ffmpegCommand,\\n      videoGenerated: true\\n    }\\n  });\\n}\\n\\nreturn processedItems;\"\n      },\n      \"id\": \"ffmpeg-video-generator\",\n      \"name\": \"FFmpeg Video Generator\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 2,\n      \"position\": [\n        2660,\n        500\n      ]\n    },\n    {\n      \"parameters\": {\n        \"resource\": \"file\",\n        \"operation\": \"upload\",\n        \"name\": \"={{ $json.title }}_video.mp4\",\n        \"parents\": {\n          \"mode\": \"list\",\n          \"value\": \"YOUR_GOOGLE_DRIVE_VIDEO_FOLDER_ID\"\n        },\n        \"binaryData\": true,\n        \"binaryPropertyName\": \"data\"\n      },\n      \"id\": \"upload-video-to-drive\",\n      \"name\": \"Upload Video to Drive\",\n      \"type\": \"n8n-nodes-base.googleDrive\",\n      \"typeVersion\": 3,\n      \"position\": [\n        2880,\n        400\n      ]\n    }\n  ],
  "connections": {
    "Google Drive Trigger": {
      "main": [
        [
          {
            "node": "File Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "File Validator": {
      "main": [
        [
          {
            "node": "If Valid PDF",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Valid PDF": {
      "main": [
        [
          {
            "node": "Download PDF",
            "type": "main",
            "index": 0
          }
        ],
        []
      ]
    },
    "Download PDF": {
      "main": [
        [
          {
            "node": "Extract PDF Text",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract PDF Text": {
      "main": [
        [
          {
            "node": "Text Preprocessor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Text Preprocessor": {
      "main": [
        [
          {
            "node": "Optimize Text for TTS",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Optimize Text for TTS": {
      "main": [
        [
          {
            "node": "Generate Audio (ElevenLabs)",
            "type": "main",
            "index": 0
          },
          {
            "node": "Generate Audio (OpenAI TTS)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Audio (ElevenLabs)": {
      "main": [
        [
          {
            "node": "Audio Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Audio (OpenAI TTS)": {
      "main": [
        [
          {
            "node": "Audio Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Audio Processor": {
      "main": [
        [
          {
            "node": "Upload Audio to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload Audio to Drive": {
      "main": [
        [
          {
            "node": "Prepare Video Generation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare Video Generation": {
      "main": [
        [
          {
            "node": "Generate Video (Creatomate)",
            "type": "main",
            "index": 0
          },
          {
            "node": "FFmpeg Video Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Video (Creatomate)": {
      "main": [
        [
          {
            "node": "Upload Video to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "FFmpeg Video Generator": {
      "main": [
        [
          {
            "node": "Upload Video to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload Video to Drive": {
      "main": [
        [
          {
            "node": "Upload to YouTube",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload to YouTube": {
      "main": [
        [
          {
            "node": "Telegram Notification",
            "type": "main",
            "index": 0
          },
          {
            "node": "Discord Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Valid PDF": {
      "main": [
        [],
        [
          {
            "node": "Error Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": {},
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2025-01-22T10:00:00.000Z",
  "versionId": "1"
}
