# PDF Audiobook Generator & YouTube Uploader

Bu n8n workflow'u PDF e-kitaplarını otomatik olarak sesli kitap videolarına dönüştürür ve YouTube'a yükler.

## 🎯 Özellikler

- ✅ **Otomatik PDF İzleme**: Google Drive'da yeni PDF dosyalarını otomatik izler
- ✅ **Akıllı Metin İşleme**: PDF'den metni çı<PERSON>ı<PERSON>, temizler ve TTS için optimize eder
- ✅ **Çoklu TTS Desteği**: ElevenLabs, OpenAI TTS ve OpenTTS seçenekleri
- ✅ **Video Oluşturma**: Ses dosyasını video formatına dönüştürür
- ✅ **YouTube Entegrasyonu**: Otomatik video yükleme ve metadata ayarlama
- ✅ **Bildirim Sistemi**: Telegram/Discord ile durum bildirimleri
- ✅ **Hata Yönetimi**: Kapsamlı hata yakalama ve bildirim

## 🚀 Hızlı Başlangıç

### 1. Workflow'u İçe Aktarın
```bash
# pdf-audiobook-workflow-fixed.json dosyasını n8n'e import edin
```

### 2. Gerekli API Anahtarları
- **Google Cloud Platform**: Drive API ve YouTube API
- **ElevenLabs**: TTS için API anahtarı
- **Telegram**: Bot token (bildirimler için)

### 3. Konfigürasyon
Workflow'daki şu değerleri güncelleyin:
- `YOUR_GOOGLE_DRIVE_FOLDER_ID`: PDF'lerin yükleneceği klasör
- `YOUR_ELEVENLABS_API_KEY`: ElevenLabs API anahtarı
- `YOUR_VOICE_ID`: ElevenLabs ses ID'si
- `YOUR_TELEGRAM_CHAT_ID`: Telegram chat ID'si

## 📁 Klasör Yapısı

```
📁 PDF Audiobooks/
├── 📁 Input PDFs/          (PDF'lerin yükleneceği klasör)
├── 📁 Audio Files/         (Oluşturulan ses dosyaları)
└── 📁 Processed/           (İşlenmiş PDF'ler)
```

## 🔧 Detaylı Kurulum

### Google Cloud Platform
1. Google Cloud Console'da yeni proje oluşturun
2. Google Drive API'yi etkinleştirin
3. YouTube Data API v3'ü etkinleştirin
4. OAuth 2.0 credentials oluşturun

### ElevenLabs
1. https://elevenlabs.io adresinde hesap oluşturun
2. API anahtarınızı alın
3. Kullanmak istediğiniz ses modelinin ID'sini not edin

### Telegram Bot
1. @BotFather ile konuşarak bot oluşturun
2. Bot token'ını alın
3. @get_id_bot ile chat ID'nizi öğrenin

## 🎬 Workflow Adımları

1. **PDF Monitoring**: Google Drive'da yeni PDF dosyası algılanır
2. **File Validation**: Dosya türü ve boyutu kontrol edilir
3. **PDF Processing**: Metin çıkarılır ve temizlenir
4. **Text Chunking**: Metin TTS için uygun parçalara bölünür
5. **Audio Generation**: ElevenLabs ile ses dosyası oluşturulur
6. **Audio Upload**: Ses dosyası Google Drive'a yüklenir
7. **Video Creation**: Ses dosyasından video oluşturulur
8. **YouTube Upload**: Video YouTube'a yüklenir
9. **Notification**: Telegram ile tamamlanma bildirimi gönderilir

## ⚙️ Konfigürasyon Seçenekleri

### TTS Ayarları (ElevenLabs)
```json
{
  "stability": 0.5,
  "similarity_boost": 0.8,
  "style": 0.2,
  "use_speaker_boost": true
}
```

### YouTube Metadata
- **Kategori**: Education (27)
- **Gizlilik**: Public
- **Etiketler**: audiobook, AI, text-to-speech, automated
- **Açıklama**: Otomatik oluşturulan açıklama template'i

## 🔍 Hata Ayıklama

### Yaygın Hatalar
1. **PDF Okuma Hatası**: PDF'in şifreli olmadığından emin olun
2. **TTS API Hatası**: API anahtarlarını ve quota'ları kontrol edin
3. **YouTube Upload Hatası**: OAuth token'ının geçerli olduğunu kontrol edin

### Log Kontrolü
- n8n execution history'yi inceleyin
- Her node'un output'unu kontrol edin
- Error mesajlarını analiz edin

## 💰 Maliyet Tahmini

- **ElevenLabs**: ~$0.30 per 1000 karakter
- **Google APIs**: Ücretsiz (quota limitleri dahilinde)
- **n8n**: Hosting maliyeti

## 🔒 Güvenlik

- Sadece telif hakkı olmayan kitapları işleyin
- API anahtarlarını güvenli tutun
- Workflow'u test ortamında deneyin

## 📞 Destek

Sorunlar için:
1. GitHub Issues'da sorun bildirin
2. n8n community'de yardım isteyin
3. API provider'ların dokümantasyonunu kontrol edin

---

**Geliştirici**: inkbytefo  
**Versiyon**: 1.0  
**Lisans**: MIT
