{
  "name": "PDF Audiobook Generator & YouTube Uploader - Complete",
  "nodes": [
    {
      "parameters": {
        "triggerOn": "specificFolder",
        "event": "fileCreated",
        "folderToWatch": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_FOLDER_ID"
        },
        "authentication": "oAuth2"
      },
      "id": "google-drive-trigger",
      "name": "Google Drive Trigger",
      "type": "n8n-nodes-base.googleDriveTrigger",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "jsCode": "// File validation for PDF uploads\nconst items = $input.all();\nconst validItems = [];\n\nfor (const item of items) {\n  const fileName = item.json.name || '';\n  const fileSize = item.json.size || 0;\n  const mimeType = item.json.mimeType || '';\n  \n  if (mimeType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf')) {\n    if (fileSize <= 50 * 1024 * 1024) {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: true,\n          validationMessage: 'File is valid PDF'\n        }\n      });\n    } else {\n      validItems.push({\n        json: {\n          ...item.json,\n          isValid: false,\n          validationMessage: 'File too large (max 50MB)'\n        }\n      });\n    }\n  } else {\n    validItems.push({\n      json: {\n        ...item.json,\n        isValid: false,\n        validationMessage: 'File is not a PDF'\n      }\n    });\n  }\n}\n\nreturn validItems;"
      },
      "id": "file-validator",
      "name": "File Validator",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "valid-pdf-condition",
              "leftValue": "={{ $json.isValid }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "if-valid-pdf",
      "name": "If Valid PDF",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "download",
        "fileId": "={{ $json.id }}",
        "options": {}
      },
      "id": "download-pdf",
      "name": "Download PDF",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [900, 200]
    },
    {
      "parameters": {
        "binaryPropertyName": "data",
        "encrypted": false
      },
      "id": "extract-pdf-text",
      "name": "Extract PDF Text",
      "type": "n8n-nodes-base.readPDF",
      "typeVersion": 1,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "jsCode": "// Clean and preprocess extracted text\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  let text = item.json.text || '';\n  \n  // Remove headers, footers, page numbers\n  text = text.replace(/^\\s*\\d+\\s*$/gm, '');\n  text = text.replace(/^\\s*Page \\d+.*$/gm, '');\n  \n  // Fix line breaks and paragraphs\n  text = text.replace(/([a-z])\\n([A-Z])/g, '$1. $2');\n  text = text.replace(/\\n{3,}/g, '\\n\\n');\n  text = text.replace(/\\s+/g, ' ');\n  \n  // Handle special characters\n  text = text.replace(/[""]/g, '\"');\n  text = text.replace(/['']/g, \"'\");\n  text = text.replace(/–/g, '-');\n  text = text.replace(/…/g, '...');\n  \n  // Split into chunks for TTS (max 4000 characters per chunk)\n  const chunks = [];\n  const maxChunkSize = 4000;\n  \n  while (text.length > 0) {\n    if (text.length <= maxChunkSize) {\n      chunks.push(text.trim());\n      break;\n    }\n    \n    let cutPoint = text.lastIndexOf('.', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('!', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('?', maxChunkSize);\n    if (cutPoint === -1) cutPoint = maxChunkSize;\n    \n    chunks.push(text.substring(0, cutPoint + 1).trim());\n    text = text.substring(cutPoint + 1).trim();\n  }\n  \n  const fileName = item.json.fileName || item.json.name || 'Unknown';\n  const title = fileName.replace('.pdf', '').replace(/[_-]/g, ' ');\n  \n  processedItems.push({\n    json: {\n      ...item.json,\n      cleanedText: chunks.join(' '),\n      textChunks: chunks,\n      chunkCount: chunks.length,\n      title: title,\n      totalCharacters: chunks.join('').length\n    }\n  });\n}\n\nreturn processedItems;"
      },
      "id": "text-preprocessor",
      "name": "Text Preprocessor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 200]
    },
    {
      "parameters": {
        "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "xi-api-key",
              "value": "YOUR_ELEVENLABS_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "text",
              "value": "={{ $json.textChunks[0] }}"
            },
            {
              "name": "model_id",
              "value": "eleven_multilingual_v2"
            },
            {
              "name": "voice_settings",
              "value": {
                "stability": 0.5,
                "similarity_boost": 0.8,
                "style": 0.2,
                "use_speaker_boost": true
              }
            }
          ]
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "binaryData"
            }
          }
        }
      },
      "id": "generate-audio-elevenlabs",
      "name": "Generate Audio (ElevenLabs)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [1560, 200]
    },
    {
      "parameters": {
        "url": "https://api.openai.com/v1/audio/speech",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer YOUR_OPENAI_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "tts-1"
            },
            {
              "name": "input",
              "value": "={{ $json.textChunks[0] }}"
            },
            {
              "name": "voice",
              "value": "alloy"
            },
            {
              "name": "response_format",
              "value": "mp3"
            },
            {
              "name": "speed",
              "value": 1.0
            }
          ]
        },
        "options": {
          "response": {
            "response": {
              "responseFormat": "binaryData"
            }
          }
        }
      },
      "id": "generate-audio-openai",
      "name": "Generate Audio (OpenAI TTS)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [1560, 400]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "upload",
        "name": "={{ $json.title }}_audio.mp3",
        "parents": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_AUDIO_FOLDER_ID"
        },
        "binaryData": true,
        "binaryPropertyName": "data"
      },
      "id": "upload-audio-to-drive",
      "name": "Upload Audio to Drive",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [1780, 300]
    },
    {
      "parameters": {
        "url": "https://api.creatomate.com/v1/renders",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer YOUR_CREATOMATE_API_KEY"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonParameters": {
          "parameters": [
            {
              "name": "template_id",
              "value": "YOUR_CREATOMATE_TEMPLATE_ID"
            },
            {
              "name": "modifications",
              "value": {
                "Audio-1": "={{ $json.webViewLink }}",
                "Text-1": "={{ $json.title }}",
                "Background-1": "#1a1a1a"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "generate-video-creatomate",
      "name": "Generate Video (Creatomate)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [2000, 300]
    },
    {
      "parameters": {
        "resource": "video",
        "operation": "upload",
        "title": "={{ $json.title }} - AI Generated Audiobook",
        "binaryProperty": "data",
        "categoryId": "27",
        "description": "This is an AI-generated audiobook version of {{ $json.title }}. Created using automated PDF to speech conversion.\\n\\n#audiobook #AI #textToSpeech #{{ $json.title.replace(/\\s+/g, '') }}",
        "tags": "audiobook,AI,text-to-speech,{{ $json.title }},automated",
        "privacyStatus": "public"
      },
      "id": "upload-to-youtube",
      "name": "Upload to YouTube",
      "type": "n8n-nodes-base.youTube",
      "typeVersion": 1,
      "position": [2220, 300]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "🎉 *Audiobook Generation Complete!*\\n\\n📚 **Title:** {{ $json.title }}\\n🎬 **YouTube URL:** https://youtube.com/watch?v={{ $json.id }}\\n⏱️ **Processing Time:** {{ $json.processingTime }}\\n📊 **Stats:**\\n- Characters: {{ $json.totalCharacters }}\\n- Audio Duration: {{ $json.audioDuration }}\\n- Video Size: {{ $json.videoSize }}\\n\\n✅ Successfully uploaded to YouTube!",
        "additionalFields": {
          "parse_mode": "Markdown"
        }
      },
      "id": "telegram-notification",
      "name": "Telegram Notification",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [2440, 300]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "❌ *Audiobook Generation Failed!*\\n\\n📚 **Title:** {{ $json.title }}\\n🚫 **Error:** {{ $json.error }}\\n⏱️ **Failed at:** {{ $json.timestamp }}\\n\\n Please check the workflow logs for more details.",
        "additionalFields": {
          "parse_mode": "Markdown"
        }
      },
      "id": "error-notification",
      "name": "Error Notification",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [900, 500]
    }
  ],
  "connections": {
    "Google Drive Trigger": {
      "main": [
        [
          {
            "node": "File Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "File Validator": {
      "main": [
        [
          {
            "node": "If Valid PDF",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Valid PDF": {
      "main": [
        [
          {
            "node": "Download PDF",
            "type": "main",
            "index": 0
          }
        ],
        []
      ]
    },
    "Download PDF": {
      "main": [
        [
          {
            "node": "Extract PDF Text",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract PDF Text": {
      "main": [
        [
          {
            "node": "Text Preprocessor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Text Preprocessor": {
      "main": [
        [
          {
            "node": "Generate Audio (ElevenLabs)",
            "type": "main",
            "index": 0
          },
          {
            "node": "Generate Audio (OpenAI TTS)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Audio (ElevenLabs)": {
      "main": [
        [
          {
            "node": "Upload Audio to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Audio (OpenAI TTS)": {
      "main": [
        [
          {
            "node": "Upload Audio to Drive",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload Audio to Drive": {
      "main": [
        [
          {
            "node": "Generate Video (Creatomate)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Video (Creatomate)": {
      "main": [
        [
          {
            "node": "Upload to YouTube",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Upload to YouTube": {
      "main": [
        [
          {
            "node": "Telegram Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Valid PDF": {
      "main": [
        [
          {
            "node": "Download PDF",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Error Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": {},
  "tags": ["audiobook", "automation", "pdf", "youtube", "tts"],
  "triggerCount": 1,
  "updatedAt": "2025-01-22T10:00:00.000Z",
  "versionId": "1"
}
