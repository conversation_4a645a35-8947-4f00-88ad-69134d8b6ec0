{
  "name": "Complete PDF Audiobook Generator & YouTube Uploader",
  "nodes": [
    {
      "parameters": {
        "triggerOn": "specificFolder",
        "event": "fileCreated",
        "folderToWatch": {
          "mode": "list",
          "value": "YOUR_GOOGLE_DRIVE_FOLDER_ID"
        },
        "authentication": "oAuth2"
      },
      "id": "google-drive-trigger",
      "name": "Google Drive Trigger",
      "type": "n8n-nodes-base.googleDriveTrigger",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "jsCode": "// File validation for PDF uploads\nconst items = $input.all();\nconst validItems = [];\n\nfor (const item of items) {\n  const fileName = item.json.name || '';\n  const fileSize = item.json.size || 0;\n  const mimeType = item.json.mimeType || '';\n  \n  if (mimeType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf')) {\n    if (fileSize <= 50 * 1024 * 1024) {\n      validItems.push({\n        json: { ...item.json, isValid: true, validationMessage: 'File is valid PDF' }\n      });\n    } else {\n      validItems.push({\n        json: { ...item.json, isValid: false, validationMessage: 'File too large (max 50MB)' }\n      });\n    }\n  } else {\n    validItems.push({\n      json: { ...item.json, isValid: false, validationMessage: 'File is not a PDF' }\n    });\n  }\n}\n\nreturn validItems;"
      },
      "id": "file-validator",
      "name": "File Validator",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": { "caseSensitive": true, "leftValue": "", "typeValidation": "strict" },
          "conditions": [
            {
              "id": "valid-pdf-condition",
              "leftValue": "={{ $json.isValid }}",
              "rightValue": true,
              "operator": { "type": "boolean", "operation": "equals" }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "if-valid-pdf",
      "name": "If Valid PDF",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "download",
        "fileId": "={{ $json.id }}",
        "options": {}
      },
      "id": "download-pdf",
      "name": "Download PDF",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [900, 200]
    },
    {
      "parameters": {
        "binaryPropertyName": "data",
        "encrypted": false
      },
      "id": "extract-pdf-text",
      "name": "Extract PDF Text",
      "type": "n8n-nodes-base.readPDF",
      "typeVersion": 1,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "jsCode": "// Clean and preprocess extracted text\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  let text = item.json.text || '';\n  \n  // Remove headers, footers, page numbers\n  text = text.replace(/^\\s*\\d+\\s*$/gm, '');\n  text = text.replace(/^\\s*Page \\d+.*$/gm, '');\n  \n  // Fix line breaks and paragraphs\n  text = text.replace(/([a-z])\\n([A-Z])/g, '$1. $2');\n  text = text.replace(/\\n{3,}/g, '\\n\\n');\n  text = text.replace(/\\s+/g, ' ');\n  \n  // Handle special characters\n  text = text.replace(/[""]/g, '\"');\n  text = text.replace(/['']/g, \"'\");\n  text = text.replace(/–/g, '-');\n  text = text.replace(/…/g, '...');\n  \n  // Split into chunks for TTS (max 4000 characters per chunk)\n  const chunks = [];\n  const maxChunkSize = 4000;\n  \n  while (text.length > 0) {\n    if (text.length <= maxChunkSize) {\n      chunks.push(text.trim());\n      break;\n    }\n    \n    let cutPoint = text.lastIndexOf('.', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('!', maxChunkSize);\n    if (cutPoint === -1) cutPoint = text.lastIndexOf('?', maxChunkSize);\n    if (cutPoint === -1) cutPoint = maxChunkSize;\n    \n    chunks.push(text.substring(0, cutPoint + 1).trim());\n    text = text.substring(cutPoint + 1).trim();\n  }\n  \n  const fileName = item.json.fileName || 'Unknown';\n  const title = fileName.replace('.pdf', '').replace(/[_-]/g, ' ');\n  \n  processedItems.push({\n    json: {\n      ...item.json,\n      cleanedText: text,\n      textChunks: chunks,\n      chunkCount: chunks.length,\n      title: title,\n      totalCharacters: chunks.join('').length\n    }\n  });\n}\n\nreturn processedItems;"
      },
      "id": "text-preprocessor",
      "name": "Text Preprocessor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 200]
    }
  ],
  "connections": {
    "Google Drive Trigger": {
      "main": [
        [{ "node": "File Validator", "type": "main", "index": 0 }]
      ]
    },
    "File Validator": {
      "main": [
        [{ "node": "If Valid PDF", "type": "main", "index": 0 }]
      ]
    },
    "If Valid PDF": {
      "main": [
        [{ "node": "Download PDF", "type": "main", "index": 0 }],
        []
      ]
    },
    "Download PDF": {
      "main": [
        [{ "node": "Extract PDF Text", "type": "main", "index": 0 }]
      ]
    },
    "Extract PDF Text": {
      "main": [
        [{ "node": "Text Preprocessor", "type": "main", "index": 0 }]
      ]
    }
  },
  "pinData": {},
  "settings": { "executionOrder": "v1" },
  "staticData": {},
  "tags": ["audiobook", "automation", "pdf", "youtube"],
  "triggerCount": 1,
  "updatedAt": "2025-01-22T10:00:00.000Z",
  "versionId": "1"
}
