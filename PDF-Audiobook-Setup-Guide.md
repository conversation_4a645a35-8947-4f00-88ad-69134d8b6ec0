# PDF Audiobook Generator & YouTube Uploader - Setup Guide

## 🎯 Project Overview

Bu n8n workflow'u PDF e-kitaplarını otomatik olarak sesli kitap videolarına dönüştürür ve YouTube'a yükler. Sistem şu adımları otomatik olarak gerçekleştirir:

1. **PDF Monitoring**: Google Drive'da yeni PDF dosyalarını izler
2. **Text Extraction**: PDF'den metni çıkarır ve temizler
3. **Text-to-Speech**: Metni ses dosyasına dönüştürür (ElevenLabs/OpenAI TTS)
4. **Video Generation**: Ses dosyasını video formatına dönüştürür (Creatomate)
5. **YouTube Upload**: Videoyu otomatik olarak YouTube'a yükler
6. **Notifications**: Telegram ile tamamlanma bildirimleri gönderir

## 🔧 Gerekli API Anahtarları ve Hesaplar

### 1. Google Cloud Platform
- **Google Drive API** etkinleştirin
- **YouTube Data API v3** etkinleştirin
- OAuth 2.0 credentials oluşturun
- Service Account (opsiyonel) oluşturun

### 2. Text-to-Speech Servisleri
**ElevenLabs (Önerilen)**
- Hesap oluşturun: https://elevenlabs.io
- API anahtarı alın
- Voice ID'nizi not edin

**OpenAI TTS (Alternatif)**
- OpenAI hesabı oluşturun: https://openai.com
- API anahtarı alın

### 3. Video Generation
**Creatomate**
- Hesap oluşturun: https://creatomate.com
- API anahtarı alın
- Video template oluşturun

### 4. Notifications
**Telegram**
- Bot oluşturun: @BotFather ile konuşun
- Bot token alın
- Chat ID'nizi öğrenin: @get_id_bot

## 📁 Google Drive Klasör Yapısı

```
📁 PDF Audiobooks/
├── 📁 Input PDFs/          (PDF'lerin yükleneceği klasör)
├── 📁 Audio Files/         (Oluşturulan ses dosyaları)
├── 📁 Video Files/         (Oluşturulan video dosyaları)
└── 📁 Processed/           (İşlenmiş PDF'ler)
```

## ⚙️ n8n Workflow Kurulumu

### 1. Workflow'u İçe Aktarın
```bash
# final-pdf-audiobook-workflow.json dosyasını n8n'e import edin
```

### 2. Credentials Yapılandırması

**Google Drive OAuth2**
```
Credential Type: Google Drive OAuth2
Client ID: YOUR_GOOGLE_CLIENT_ID
Client Secret: YOUR_GOOGLE_CLIENT_SECRET
Scope: https://www.googleapis.com/auth/drive
```

**YouTube OAuth2**
```
Credential Type: YouTube OAuth2
Client ID: YOUR_GOOGLE_CLIENT_ID
Client Secret: YOUR_GOOGLE_CLIENT_SECRET
Scope: https://www.googleapis.com/auth/youtube.upload
```

**ElevenLabs API**
```
Credential Type: Header Auth
Name: xi-api-key
Value: YOUR_ELEVENLABS_API_KEY
```

**OpenAI API**
```
Credential Type: Header Auth
Name: Authorization
Value: Bearer YOUR_OPENAI_API_KEY
```

**Creatomate API**
```
Credential Type: Header Auth
Name: Authorization
Value: Bearer YOUR_CREATOMATE_API_KEY
```

**Telegram Bot**
```
Credential Type: Telegram Bot
Bot Token: YOUR_TELEGRAM_BOT_TOKEN
```

### 3. Node Konfigürasyonu

**Google Drive Trigger**
- `folderToWatch`: Google Drive klasör ID'nizi girin
- `event`: "fileCreated" olarak ayarlayın

**Generate Audio (ElevenLabs)**
- `YOUR_VOICE_ID`: ElevenLabs voice ID'nizi girin
- `YOUR_ELEVENLABS_API_KEY`: API anahtarınızı girin

**Generate Audio (OpenAI TTS)**
- `YOUR_OPENAI_API_KEY`: OpenAI API anahtarınızı girin
- `voice`: "alloy", "echo", "fable", "onyx", "nova", "shimmer" seçeneklerinden birini seçin

**Upload Audio to Drive**
- `YOUR_GOOGLE_DRIVE_AUDIO_FOLDER_ID`: Ses dosyaları klasör ID'si

**Generate Video (Creatomate)**
- `YOUR_CREATOMATE_API_KEY`: Creatomate API anahtarınızı girin
- `YOUR_CREATOMATE_TEMPLATE_ID`: Video template ID'nizi girin

**Telegram Notification**
- `YOUR_TELEGRAM_CHAT_ID`: Telegram chat ID'nizi girin

## 🎨 Creatomate Video Template

Creatomate'de aşağıdaki özelliklere sahip bir template oluşturun:

```json
{
  "width": 1920,
  "height": 1080,
  "duration": "auto",
  "elements": [
    {
      "type": "composition",
      "track": 1,
      "elements": [
        {
          "type": "shape",
          "name": "Background-1",
          "fill_color": "#1a1a1a",
          "width": "100%",
          "height": "100%"
        },
        {
          "type": "text",
          "name": "Text-1",
          "text": "{{ title }}",
          "font_family": "Arial",
          "font_size": 48,
          "font_color": "#ffffff",
          "x": "50%",
          "y": "50%",
          "text_align": "center"
        },
        {
          "type": "audio",
          "name": "Audio-1",
          "source": "{{ audioUrl }}",
          "track": 1
        }
      ]
    }
  ]
}
```

## 🚀 Kullanım

1. **PDF Yükleme**: PDF dosyanızı Google Drive'daki belirlenen klasöre yükleyin
2. **Otomatik İşlem**: Workflow otomatik olarak başlar
3. **İzleme**: Telegram'dan bildirimler alın
4. **Sonuç**: YouTube'da yayınlanan videoyu kontrol edin

## 📊 İzleme ve Hata Ayıklama

### Workflow Execution Logs
- n8n interface'inde execution history'yi kontrol edin
- Her node'un output'unu inceleyin
- Error mesajlarını analiz edin

### Yaygın Hatalar ve Çözümleri

**PDF Okuma Hatası**
- PDF'in şifreli olmadığından emin olun
- Dosya boyutunun 50MB'dan küçük olduğunu kontrol edin

**TTS API Hatası**
- API anahtarlarınızın geçerli olduğunu kontrol edin
- Rate limiting'e takılmadığınızdan emin olun

**YouTube Upload Hatası**
- OAuth2 token'ının geçerli olduğunu kontrol edin
- Video metadata'sının YouTube kurallarına uygun olduğunu kontrol edin

## 💰 Maliyet Tahmini

**ElevenLabs**: ~$0.30 per 1000 characters
**OpenAI TTS**: ~$15 per 1M characters
**Creatomate**: ~$0.05 per video minute
**Google APIs**: Ücretsiz (quota limitleri dahilinde)

## 🔒 Güvenlik Notları

- Sadece telif hakkı olmayan veya kamu malı kitapları işleyin
- API anahtarlarınızı güvenli tutun
- n8n credentials'ları şifreli olarak saklayın
- Workflow'u test ortamında önce deneyin

## 📞 Destek

Sorunlar için:
1. n8n execution logs'unu kontrol edin
2. API provider'ların status sayfalarını kontrol edin
3. GitHub issues'da sorun bildirin

---

**Geliştirici**: inkbytefo
**Versiyon**: 1.0
**Son Güncelleme**: 2025-01-22
